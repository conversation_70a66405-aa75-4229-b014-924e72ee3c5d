/**
 * Component Library Detector
 * Detects and analyzes Material-UI, Ant Design, Chakra UI and other component libraries
 */

import { Page } from 'puppeteer';
import logger from '../../../utils/logger';

export interface ComponentLibraryDetection {
  library: string;
  version?: string;
  confidence: number;
  components: string[];
  theme?: {
    hasCustomTheme: boolean;
    themeProvider: string;
    colorMode: 'light' | 'dark' | 'system' | 'unknown';
    accessibility: {
      hasHighContrast: boolean;
      hasReducedMotion: boolean;
      hasFocusVisible: boolean;
    };
  };
  accessibility: {
    score: number;
    hasA11yProps: boolean;
    missingA11yProps: string[];
    commonIssues: string[];
    recommendations: string[];
  };
}

export interface ComponentLibraryConfig {
  enableMaterialUIDetection: boolean;
  enableAntDesignDetection: boolean;
  enableChakraUIDetection: boolean;
  enableBootstrapDetection: boolean;
  enableTailwindUIDetection: boolean;
  enableMantineDetection: boolean;
  enableArcoDesignDetection: boolean;
  enableSemiDesignDetection: boolean;
  deepComponentAnalysis: boolean;
  analyzeThemeAccessibility: boolean;
}

export interface ComponentAccessibilityAnalysis {
  componentType: string;
  element: string;
  hasProperRole: boolean;
  hasAccessibleName: boolean;
  hasKeyboardSupport: boolean;
  hasFocusManagement: boolean;
  hasARIAAttributes: boolean;
  missingFeatures: string[];
  score: number;
}

export interface ComponentLibraryAnalysis {
  detectedLibraries: ComponentLibraryDetection[];
  primaryLibrary: ComponentLibraryDetection;
  componentAnalysis: ComponentAccessibilityAnalysis[];
  overallAccessibilityScore: number;
  themeAccessibilityScore: number;
  recommendations: string[];
  bestPractices: string[];
  commonPatterns: string[];
}

/**
 * Component library detector with accessibility analysis
 */
export class ComponentLibraryDetector {
  private static instance: ComponentLibraryDetector;
  private config: ComponentLibraryConfig;

  private constructor(config?: Partial<ComponentLibraryConfig>) {
    this.config = {
      enableMaterialUIDetection: config?.enableMaterialUIDetection ?? true,
      enableAntDesignDetection: config?.enableAntDesignDetection ?? true,
      enableChakraUIDetection: config?.enableChakraUIDetection ?? true,
      enableBootstrapDetection: config?.enableBootstrapDetection ?? true,
      enableTailwindUIDetection: config?.enableTailwindUIDetection ?? true,
      enableMantineDetection: config?.enableMantineDetection ?? true,
      enableArcoDesignDetection: config?.enableArcoDesignDetection ?? true,
      enableSemiDesignDetection: config?.enableSemiDesignDetection ?? true,
      deepComponentAnalysis: config?.deepComponentAnalysis ?? true,
      analyzeThemeAccessibility: config?.analyzeThemeAccessibility ?? true,
    };

    logger.info('🏗️ Component Library Detector initialized', {
      materialUI: this.config.enableMaterialUIDetection,
      antDesign: this.config.enableAntDesignDetection,
      chakraUI: this.config.enableChakraUIDetection,
      deepAnalysis: this.config.deepComponentAnalysis,
    });
  }

  static getInstance(config?: Partial<ComponentLibraryConfig>): ComponentLibraryDetector {
    if (!ComponentLibraryDetector.instance) {
      ComponentLibraryDetector.instance = new ComponentLibraryDetector(config);
    }
    return ComponentLibraryDetector.instance;
  }

  /**
   * Analyze component libraries on the page
   */
  async analyzeComponentLibraries(page: Page): Promise<ComponentLibraryAnalysis> {
    logger.debug('🏗️ Starting component library analysis');

    // Inject component library detection functions
    await this.injectComponentLibraryDetection(page);

    // Detect all component libraries
    const detectedLibraries = await this.detectComponentLibraries(page);

    // Determine primary library
    const primaryLibrary = detectedLibraries.reduce(
      (prev, current) => (current.confidence > prev.confidence ? current : prev),
      detectedLibraries[0] || this.getDefaultLibrary(),
    );

    // Analyze components if deep analysis is enabled
    const componentAnalysis = this.config.deepComponentAnalysis
      ? await this.analyzeComponents(page, detectedLibraries)
      : [];

    // Calculate scores
    const overallAccessibilityScore = this.calculateOverallAccessibilityScore(
      detectedLibraries,
      componentAnalysis,
    );
    const themeAccessibilityScore = this.calculateThemeAccessibilityScore(detectedLibraries);

    // Generate recommendations and best practices
    const recommendations = this.generateRecommendations(detectedLibraries, componentAnalysis);
    const bestPractices = this.generateBestPractices(detectedLibraries);
    const commonPatterns = this.identifyCommonPatterns(detectedLibraries, componentAnalysis);

    return {
      detectedLibraries,
      primaryLibrary,
      componentAnalysis,
      overallAccessibilityScore,
      themeAccessibilityScore,
      recommendations,
      bestPractices,
      commonPatterns,
    };
  }

  /**
   * Inject component library detection functions
   */
  private async injectComponentLibraryDetection(page: Page): Promise<void> {
    await page.evaluateOnNewDocument(() => {
      (window as Window & { componentLibraryDetection?: unknown }).componentLibraryDetection = {
        /**
         * Detect all component libraries
         */
        detectAllLibraries() {
          const libraries = [
            this.detectMaterialUI(),
            this.detectAntDesign(),
            this.detectChakraUI(),
            this.detectBootstrap(),
            this.detectTailwindUI(),
            this.detectMantine(),
            this.detectArcoDesign(),
            this.detectSemiDesign(),
          ].filter((library) => library.confidence > 0);

          return libraries;
        },

        /**
         * Detect Material-UI (MUI)
         */
        detectMaterialUI() {
          let confidence = 0;
          const components: string[] = [];
          const version = null;

          // Check for MUI classes
          if (document.querySelector('[class*="MuiButton-"]')) {
            confidence += 0.3;
            components.push('Button');
          }

          if (document.querySelector('[class*="MuiTextField-"]')) {
            confidence += 0.2;
            components.push('TextField');
          }

          if (document.querySelector('[class*="MuiAppBar-"]')) {
            confidence += 0.2;
            components.push('AppBar');
          }

          if (document.querySelector('[class*="MuiDrawer-"]')) {
            confidence += 0.1;
            components.push('Drawer');
          }

          // Check for MUI theme provider
          if (document.querySelector('[class*="MuiCssBaseline-"]')) {
            confidence += 0.1;
          }

          // Check for MUI globals
          if ((window as Window & { mui?: unknown; __MUI_THEME__?: unknown }).mui ||
              (window as Window & { mui?: unknown; __MUI_THEME__?: unknown }).__MUI_THEME__) {
            confidence += 0.1;
          }

          const theme = this.analyzeMUITheme();
          const accessibility = this.analyzeMUIAccessibility();

          return {
            library: 'material-ui',
            version,
            confidence: Math.min(confidence, 1),
            components,
            theme,
            accessibility,
          };
        },

        /**
         * Detect Ant Design
         */
        detectAntDesign() {
          let confidence = 0;
          const components: string[] = [];
          const version = null;

          // Check for Ant Design classes
          if (document.querySelector('.ant-btn')) {
            confidence += 0.3;
            components.push('Button');
          }

          if (document.querySelector('.ant-input')) {
            confidence += 0.2;
            components.push('Input');
          }

          if (document.querySelector('.ant-table')) {
            confidence += 0.2;
            components.push('Table');
          }

          if (document.querySelector('.ant-menu')) {
            confidence += 0.1;
            components.push('Menu');
          }

          if (document.querySelector('.ant-layout')) {
            confidence += 0.1;
            components.push('Layout');
          }

          // Check for Ant Design config provider
          if (document.querySelector('.ant-config-provider')) {
            confidence += 0.1;
          }

          const theme = this.analyzeAntDesignTheme();
          const accessibility = this.analyzeAntDesignAccessibility();

          return {
            library: 'ant-design',
            version,
            confidence: Math.min(confidence, 1),
            components,
            theme,
            accessibility,
          };
        },

        /**
         * Detect Chakra UI
         */
        detectChakraUI() {
          let confidence = 0;
          const components: string[] = [];
          const version = null;

          // Check for Chakra UI attributes
          if (document.querySelector('[data-theme]')) {
            confidence += 0.2;
          }

          // Check for Chakra UI classes
          if (document.querySelector('[class*="chakra-"]')) {
            confidence += 0.3;
            components.push('ChakraComponent');
          }

          if (document.querySelector('[class*="css-"]')) {
            confidence += 0.1; // Emotion CSS-in-JS (used by Chakra)
          }

          // Check for Chakra UI specific components
          if (document.querySelector('[role="button"][class*="chakra-button"]')) {
            confidence += 0.2;
            components.push('Button');
          }

          if (document.querySelector('[class*="chakra-stack"]')) {
            confidence += 0.1;
            components.push('Stack');
          }

          const theme = this.analyzeChakraUITheme();
          const accessibility = this.analyzeChakraUIAccessibility();

          return {
            library: 'chakra-ui',
            version,
            confidence: Math.min(confidence, 1),
            components,
            theme,
            accessibility,
          };
        },

        /**
         * Detect Bootstrap
         */
        detectBootstrap() {
          let confidence = 0;
          const components: string[] = [];
          const version = null;

          // Check for Bootstrap classes
          if (document.querySelector('.btn')) {
            confidence += 0.2;
            components.push('Button');
          }

          if (document.querySelector('.container, .container-fluid')) {
            confidence += 0.2;
            components.push('Container');
          }

          if (document.querySelector('.row')) {
            confidence += 0.1;
            components.push('Grid');
          }

          if (document.querySelector('.navbar')) {
            confidence += 0.2;
            components.push('Navbar');
          }

          if (document.querySelector('.modal')) {
            confidence += 0.1;
            components.push('Modal');
          }

          // Check for Bootstrap CSS
          const bootstrapCSS = Array.from(document.querySelectorAll('link[href*="bootstrap"]'));
          if (bootstrapCSS.length > 0) {
            confidence += 0.2;
          }

          const theme = this.analyzeBootstrapTheme();
          const accessibility = this.analyzeBootstrapAccessibility();

          return {
            library: 'bootstrap',
            version,
            confidence: Math.min(confidence, 1),
            components,
            theme,
            accessibility,
          };
        },

        /**
         * Detect Tailwind UI
         */
        detectTailwindUI() {
          let confidence = 0;
          const components: string[] = [];

          // Check for Tailwind CSS classes
          const tailwindClasses = [
            'bg-',
            'text-',
            'p-',
            'm-',
            'w-',
            'h-',
            'flex',
            'grid',
            'rounded',
            'shadow',
            'border',
            'hover:',
            'focus:',
            'active:',
          ];

          let tailwindClassCount = 0;
          tailwindClasses.forEach((className) => {
            if (document.querySelector(`[class*="${className}"]`)) {
              tailwindClassCount++;
            }
          });

          if (tailwindClassCount > 5) {
            confidence += 0.4;
            components.push('TailwindComponents');
          }

          // Check for Headless UI (often used with Tailwind)
          if (document.querySelector('[data-headlessui-state]')) {
            confidence += 0.3;
            components.push('HeadlessUI');
          }

          const theme = this.analyzeTailwindTheme();
          const accessibility = this.analyzeTailwindAccessibility();

          return {
            library: 'tailwind-ui',
            version: null,
            confidence: Math.min(confidence, 1),
            components,
            theme,
            accessibility,
          };
        },

        /**
         * Detect Mantine
         */
        detectMantine() {
          let confidence = 0;
          const components: string[] = [];

          // Check for Mantine classes
          if (document.querySelector('[class*="mantine-"]')) {
            confidence += 0.4;
            components.push('MantineComponent');
          }

          if (document.querySelector('[data-mantine-color-scheme]')) {
            confidence += 0.3;
          }

          const theme = this.analyzeMantineTheme();
          const accessibility = this.analyzeMantineAccessibility();

          return {
            library: 'mantine',
            version: null,
            confidence: Math.min(confidence, 1),
            components,
            theme,
            accessibility,
          };
        },

        /**
         * Detect Arco Design
         */
        detectArcoDesign() {
          let confidence = 0;
          const components: string[] = [];

          if (document.querySelector('.arco-btn')) {
            confidence += 0.3;
            components.push('Button');
          }

          if (document.querySelector('.arco-input')) {
            confidence += 0.2;
            components.push('Input');
          }

          const accessibility = this.analyzeArcoDesignAccessibility();

          return {
            library: 'arco-design',
            version: null,
            confidence: Math.min(confidence, 1),
            components,
            accessibility,
          };
        },

        /**
         * Detect Semi Design
         */
        detectSemiDesign() {
          let confidence = 0;
          const components: string[] = [];

          if (document.querySelector('.semi-button')) {
            confidence += 0.3;
            components.push('Button');
          }

          if (document.querySelector('.semi-input')) {
            confidence += 0.2;
            components.push('Input');
          }

          const accessibility = this.analyzeSemiDesignAccessibility();

          return {
            library: 'semi-design',
            version: null,
            confidence: Math.min(confidence, 1),
            components,
            accessibility,
          };
        },

        /**
         * Theme analysis methods
         */
        analyzeMUITheme() {
          const hasCustomTheme = !!(window as Window & { __MUI_THEME__?: unknown }).__MUI_THEME__;
          const themeProvider = document.querySelector('[class*="MuiThemeProvider-"]')
            ? 'ThemeProvider'
            : 'none';

          return {
            hasCustomTheme,
            themeProvider,
            colorMode: this.detectColorMode(),
            accessibility: this.analyzeThemeAccessibility(),
          };
        },

        analyzeAntDesignTheme() {
          const hasCustomTheme = !!document.querySelector('.ant-config-provider');

          return {
            hasCustomTheme,
            themeProvider: hasCustomTheme ? 'ConfigProvider' : 'none',
            colorMode: this.detectColorMode(),
            accessibility: this.analyzeThemeAccessibility(),
          };
        },

        analyzeChakraUITheme() {
          const hasCustomTheme = !!document.querySelector('[data-theme]');

          return {
            hasCustomTheme,
            themeProvider: hasCustomTheme ? 'ChakraProvider' : 'none',
            colorMode: this.detectColorMode(),
            accessibility: this.analyzeThemeAccessibility(),
          };
        },

        analyzeBootstrapTheme() {
          const hasCustomTheme = !!document.querySelector('[data-bs-theme]');

          return {
            hasCustomTheme,
            themeProvider: 'none',
            colorMode: this.detectColorMode(),
            accessibility: this.analyzeThemeAccessibility(),
          };
        },

        analyzeTailwindTheme() {
          const hasCustomTheme = !!document.querySelector('[class*="dark:"]');

          return {
            hasCustomTheme,
            themeProvider: 'none',
            colorMode: this.detectColorMode(),
            accessibility: this.analyzeThemeAccessibility(),
          };
        },

        analyzeMantineTheme() {
          const hasCustomTheme = !!document.querySelector('[data-mantine-color-scheme]');

          return {
            hasCustomTheme,
            themeProvider: hasCustomTheme ? 'MantineProvider' : 'none',
            colorMode: this.detectColorMode(),
            accessibility: this.analyzeThemeAccessibility(),
          };
        },

        /**
         * Accessibility analysis methods
         */
        analyzeMUIAccessibility() {
          const score = this.calculateLibraryA11yScore('material-ui');
          const hasA11yProps = !!document.querySelector(
            '[aria-label], [aria-labelledby], [aria-describedby]',
          );
          const missingA11yProps = this.findMissingA11yProps('material-ui');
          const commonIssues = this.identifyCommonA11yIssues('material-ui');
          const recommendations = this.generateA11yRecommendations('material-ui');

          return { score, hasA11yProps, missingA11yProps, commonIssues, recommendations };
        },

        analyzeAntDesignAccessibility() {
          const score = this.calculateLibraryA11yScore('ant-design');
          const hasA11yProps = !!document.querySelector(
            '[aria-label], [aria-labelledby], [aria-describedby]',
          );
          const missingA11yProps = this.findMissingA11yProps('ant-design');
          const commonIssues = this.identifyCommonA11yIssues('ant-design');
          const recommendations = this.generateA11yRecommendations('ant-design');

          return { score, hasA11yProps, missingA11yProps, commonIssues, recommendations };
        },

        analyzeChakraUIAccessibility() {
          const score = this.calculateLibraryA11yScore('chakra-ui');
          const hasA11yProps = !!document.querySelector(
            '[aria-label], [aria-labelledby], [aria-describedby]',
          );
          const missingA11yProps = this.findMissingA11yProps('chakra-ui');
          const commonIssues = this.identifyCommonA11yIssues('chakra-ui');
          const recommendations = this.generateA11yRecommendations('chakra-ui');

          return { score, hasA11yProps, missingA11yProps, commonIssues, recommendations };
        },

        analyzeBootstrapAccessibility() {
          const score = this.calculateLibraryA11yScore('bootstrap');
          const hasA11yProps = !!document.querySelector(
            '[aria-label], [aria-labelledby], [aria-describedby]',
          );
          const missingA11yProps = this.findMissingA11yProps('bootstrap');
          const commonIssues = this.identifyCommonA11yIssues('bootstrap');
          const recommendations = this.generateA11yRecommendations('bootstrap');

          return { score, hasA11yProps, missingA11yProps, commonIssues, recommendations };
        },

        analyzeTailwindAccessibility() {
          const score = this.calculateLibraryA11yScore('tailwind-ui');
          const hasA11yProps = !!document.querySelector(
            '[aria-label], [aria-labelledby], [aria-describedby]',
          );
          const missingA11yProps = this.findMissingA11yProps('tailwind-ui');
          const commonIssues = this.identifyCommonA11yIssues('tailwind-ui');
          const recommendations = this.generateA11yRecommendations('tailwind-ui');

          return { score, hasA11yProps, missingA11yProps, commonIssues, recommendations };
        },

        analyzeMantineAccessibility() {
          const score = this.calculateLibraryA11yScore('mantine');
          const hasA11yProps = !!document.querySelector(
            '[aria-label], [aria-labelledby], [aria-describedby]',
          );
          const missingA11yProps = this.findMissingA11yProps('mantine');
          const commonIssues = this.identifyCommonA11yIssues('mantine');
          const recommendations = this.generateA11yRecommendations('mantine');

          return { score, hasA11yProps, missingA11yProps, commonIssues, recommendations };
        },

        analyzeArcoDesignAccessibility() {
          const score = this.calculateLibraryA11yScore('arco-design');
          const hasA11yProps = !!document.querySelector(
            '[aria-label], [aria-labelledby], [aria-describedby]',
          );
          const missingA11yProps = this.findMissingA11yProps('arco-design');
          const commonIssues = this.identifyCommonA11yIssues('arco-design');
          const recommendations = this.generateA11yRecommendations('arco-design');

          return { score, hasA11yProps, missingA11yProps, commonIssues, recommendations };
        },

        analyzeSemiDesignAccessibility() {
          const score = this.calculateLibraryA11yScore('semi-design');
          const hasA11yProps = !!document.querySelector(
            '[aria-label], [aria-labelledby], [aria-describedby]',
          );
          const missingA11yProps = this.findMissingA11yProps('semi-design');
          const commonIssues = this.identifyCommonA11yIssues('semi-design');
          const recommendations = this.generateA11yRecommendations('semi-design');

          return { score, hasA11yProps, missingA11yProps, commonIssues, recommendations };
        },

        /**
         * Helper methods
         */
        detectColorMode() {
          if (document.documentElement.classList.contains('dark')) return 'dark';
          if (document.documentElement.classList.contains('light')) return 'light';
          if (document.querySelector('[data-theme="dark"]')) return 'dark';
          if (document.querySelector('[data-theme="light"]')) return 'light';
          return 'unknown';
        },

        analyzeThemeAccessibility() {
          return {
            hasHighContrast: !!document.querySelector('[data-high-contrast], .high-contrast'),
            hasReducedMotion: !!document.querySelector('[data-reduced-motion], .reduced-motion'),
            hasFocusVisible: !!document.querySelector(':focus-visible, .focus-visible'),
          };
        },

        calculateLibraryA11yScore(_library: string): number {
          let score = 50; // Base score

          // Check for ARIA attributes
          const ariaElements = document.querySelectorAll(
            '[aria-label], [aria-labelledby], [aria-describedby]',
          );
          score += Math.min(ariaElements.length * 2, 20);

          // Check for semantic HTML
          const semanticElements = document.querySelectorAll(
            'button, input, select, textarea, nav, main, aside, header, footer',
          );
          score += Math.min(semanticElements.length, 15);

          // Check for focus management
          const focusableElements = document.querySelectorAll(
            '[tabindex], button, input, select, textarea, a[href]',
          );
          score += Math.min(focusableElements.length, 15);

          return Math.min(score, 100);
        },

        findMissingA11yProps(_library: string): string[] {
          const missing: string[] = [];

          // Check buttons without accessible names
          const buttons = document.querySelectorAll('button, [role="button"]');
          buttons.forEach((button) => {
            if (!button.textContent?.trim() && !button.getAttribute('aria-label')) {
              missing.push('aria-label on button');
            }
          });

          // Check inputs without labels
          const inputs = document.querySelectorAll('input, select, textarea');
          inputs.forEach((input) => {
            const id = input.id;
            const hasLabel = id && document.querySelector(`label[for="${id}"]`);
            const hasAriaLabel = input.getAttribute('aria-label');
            if (!hasLabel && !hasAriaLabel) {
              missing.push('label or aria-label on input');
            }
          });

          return missing.slice(0, 5); // Limit to 5 items
        },

        identifyCommonA11yIssues(library: string): string[] {
          // Library-specific common issues
          const libraryIssues: Record<string, string[]> = {
            'material-ui': [
              'Missing focus indicators on custom components',
              'Insufficient color contrast in default theme',
            ],
            'ant-design': [
              'Complex components may lack keyboard navigation',
              'Table accessibility needs attention',
            ],
            'chakra-ui': ['Custom theme may affect contrast ratios', 'Focus management in modals'],
            bootstrap: [
              'Requires manual ARIA implementation',
              'Default focus indicators may be insufficient',
            ],
            'tailwind-ui': [
              'No built-in accessibility features',
              'Requires manual implementation of all a11y features',
            ],
            mantine: ['Focus trap implementation in modals', 'Color contrast in dark theme'],
          };

          return libraryIssues[library] || [];
        },

        generateA11yRecommendations(library: string): string[] {
          // Library-specific recommendations
          const libraryRecs: Record<string, string[]> = {
            'material-ui': [
              'Use MUI accessibility props consistently',
              'Test with screen readers',
              'Implement proper focus management',
            ],
            'ant-design': [
              'Use Ant Design accessibility guidelines',
              'Add ARIA labels to complex components',
              'Test keyboard navigation',
            ],
            'chakra-ui': [
              'Leverage Chakra UI accessibility features',
              'Use focus management hooks',
              'Test color contrast ratios',
            ],
            bootstrap: [
              'Implement ARIA patterns manually',
              'Use semantic HTML elements',
              'Add proper focus indicators',
            ],
            'tailwind-ui': [
              'Use Headless UI for accessible components',
              'Implement ARIA patterns',
              'Add focus management',
            ],
            mantine: [
              'Use Mantine accessibility hooks',
              'Test focus management',
              'Verify color contrast',
            ],
          };

          return (
            libraryRecs[library] || [
              'Follow accessibility best practices',
              'Test with assistive technologies',
            ]
          );
        },
      };
    });
  }

  /**
   * Detect component libraries
   */
  private async detectComponentLibraries(page: Page): Promise<ComponentLibraryDetection[]> {
    return await page.evaluate(() => {
      return (
        window as Window & {
          componentLibraryDetection?: { detectAllLibraries: () => ComponentLibraryDetection[] };
        }
      ).componentLibraryDetection?.detectAllLibraries() || [];
    });
  }

  /**
   * Analyze individual components
   */
  private async analyzeComponents(
    page: Page,
    libraries: ComponentLibraryDetection[],
  ): Promise<ComponentAccessibilityAnalysis[]> {
    // First inject component analysis functions
    await page.evaluate(() => {
      const windowWithDetection = window as Window & { componentLibraryDetection?: Record<string, unknown> };
      if (!windowWithDetection.componentLibraryDetection) {
        windowWithDetection.componentLibraryDetection = {};
      }

      (windowWithDetection.componentLibraryDetection as Record<string, unknown>).analyzeComponentAccessibility = function (
        element: Element,
        componentType: string,
        _library: string,
      ) {
        const detectionObj = windowWithDetection.componentLibraryDetection as Record<string, unknown>;
        const analysis = {
          componentType,
          hasProperRole: (detectionObj.checkProperRole as (el: Element, type: string) => boolean)?.(element, componentType) ?? false,
          hasAccessibleName: (detectionObj.checkAccessibleName as (el: Element) => boolean)?.(element) ?? false,
          hasKeyboardSupport: (detectionObj.checkKeyboardSupport as (el: Element) => boolean)?.(element) ?? false,
          hasFocusManagement: (detectionObj.checkFocusManagement as (el: Element) => boolean)?.(element) ?? false,
          hasARIAAttributes: (detectionObj.checkARIAAttributes as (el: Element) => boolean)?.(element) ?? false,
          missingFeatures: [] as string[],
          score: 0,
        };

        // Calculate score based on features
        let score = 0;
        if (analysis.hasProperRole) score += 20;
        if (analysis.hasAccessibleName) score += 25;
        if (analysis.hasKeyboardSupport) score += 20;
        if (analysis.hasFocusManagement) score += 15;
        if (analysis.hasARIAAttributes) score += 20;

        // Identify missing features
        if (!analysis.hasProperRole) analysis.missingFeatures.push('proper role');
        if (!analysis.hasAccessibleName) analysis.missingFeatures.push('accessible name');
        if (!analysis.hasKeyboardSupport) analysis.missingFeatures.push('keyboard support');
        if (!analysis.hasFocusManagement) analysis.missingFeatures.push('focus management');
        if (!analysis.hasARIAAttributes) analysis.missingFeatures.push('ARIA attributes');

        analysis.score = score;
        return analysis;
      };

      (windowWithDetection.componentLibraryDetection as Record<string, unknown>).checkProperRole = function (element: Element, _componentType: string) {
        const tagName = element.tagName.toLowerCase();
        const role = element.getAttribute('role');

        // Check if element has appropriate role
        if (tagName === 'button' || role === 'button') return true;
        if (tagName === 'input' || tagName === 'select' || tagName === 'textarea') return true;
        if (tagName === 'a' && element.getAttribute('href')) return true;

        return false;
      };

      (windowWithDetection.componentLibraryDetection as Record<string, unknown>).checkAccessibleName = function (element: Element) {
        // Check for accessible name
        if (element.textContent?.trim()) return true;
        if (element.getAttribute('aria-label')) return true;
        if (element.getAttribute('aria-labelledby')) return true;
        if (element.getAttribute('alt')) return true;
        if (element.getAttribute('title')) return true;

        return false;
      };

      (windowWithDetection.componentLibraryDetection as Record<string, unknown>).checkKeyboardSupport = function (element: Element) {
        // Check if element is keyboard accessible
        const tabIndex = element.getAttribute('tabindex');
        if (tabIndex === '-1') return false;

        const tagName = element.tagName.toLowerCase();
        const role = element.getAttribute('role');

        // Interactive elements are typically keyboard accessible
        if (['button', 'input', 'select', 'textarea', 'a'].includes(tagName)) return true;
        if (['button', 'link', 'textbox', 'combobox', 'checkbox', 'radio'].includes(role || ''))
          return true;

        return false;
      };

      (windowWithDetection.componentLibraryDetection as Record<string, unknown>).checkFocusManagement = function (element: Element) {
        // Check for focus management features
        const style = window.getComputedStyle(element as HTMLElement);

        // Check for focus indicators
        if (style.outline !== 'none') return true;
        if (style.boxShadow !== 'none') return true;

        return false;
      };

      (windowWithDetection.componentLibraryDetection as Record<string, unknown>).checkARIAAttributes = function (element: Element) {
        // Check for ARIA attributes
        const ariaAttributes = [
          'aria-label',
          'aria-labelledby',
          'aria-describedby',
          'aria-expanded',
          'aria-selected',
          'aria-checked',
          'aria-pressed',
          'aria-hidden',
          'aria-live',
          'aria-atomic',
          'aria-relevant',
        ];

        return ariaAttributes.some((attr) => element.hasAttribute(attr));
      };
    });

    return await page.evaluate(
      (libraryNames: string[]) => {
        interface ComponentAnalysisData {
          componentType: string;
          element: string;
          hasProperRole: boolean;
          hasAccessibleName: boolean;
          hasKeyboardSupport: boolean;
          hasFocusManagement: boolean;
          hasARIAAttributes: boolean;
          missingFeatures: string[];
          score: number;
        }

        const analyses: ComponentAnalysisData[] = [];

        // Define component selectors for each library
        const componentSelectors: Record<string, Record<string, string>> = {
          'material-ui': {
            Button: '[class*="MuiButton-"]',
            TextField: '[class*="MuiTextField-"]',
            Select: '[class*="MuiSelect-"]',
            Checkbox: '[class*="MuiCheckbox-"]',
            Radio: '[class*="MuiRadio-"]',
          },
          'ant-design': {
            Button: '.ant-btn',
            Input: '.ant-input',
            Select: '.ant-select',
            Checkbox: '.ant-checkbox',
            Radio: '.ant-radio',
          },
          'chakra-ui': {
            Button: '[class*="chakra-button"]',
            Input: '[class*="chakra-input"]',
            Select: '[class*="chakra-select"]',
            Checkbox: '[class*="chakra-checkbox"]',
          },
          bootstrap: {
            Button: '.btn',
            Input: '.form-control',
            Select: '.form-select',
            Checkbox: '.form-check-input[type="checkbox"]',
            Radio: '.form-check-input[type="radio"]',
          },
        };

        // Analyze components for each detected library
        libraryNames.forEach((libraryName) => {
          const selectors = componentSelectors[libraryName];
          if (!selectors) return;

          Object.entries(selectors).forEach(([componentType, selector]) => {
            const elements = document.querySelectorAll(selector);

            elements.forEach((element, index) => {
              const detectionObj = (
                window as Window & { componentLibraryDetection?: Record<string, unknown> }
              ).componentLibraryDetection;

              const analyzeFunc = detectionObj?.analyzeComponentAccessibility as
                ((el: Element, type: string, lib: string) => ComponentAccessibilityAnalysis) | undefined;

              const analysis = analyzeFunc?.(element, componentType, libraryName);

              if (analysis) {
                analyses.push({
                  ...analysis,
                  element: `${componentType}-${index}`,
                });
              }
            });
          });
        });

        return analyses;
      },
      libraries.map((lib) => lib.library),
    );
  }

  /**
   * Calculate overall accessibility score
   */
  private calculateOverallAccessibilityScore(
    libraries: ComponentLibraryDetection[],
    componentAnalysis: ComponentAccessibilityAnalysis[],
  ): number {
    if (libraries.length === 0) return 50;

    // Calculate weighted average of library scores
    let totalScore = 0;
    let totalWeight = 0;

    libraries.forEach((library) => {
      totalScore += library.accessibility.score * library.confidence;
      totalWeight += library.confidence;
    });

    // Factor in component analysis if available
    if (componentAnalysis.length > 0) {
      const componentScore =
        componentAnalysis.reduce((sum, comp) => sum + comp.score, 0) / componentAnalysis.length;
      totalScore += componentScore * 0.3;
      totalWeight += 0.3;
    }

    return totalWeight > 0 ? Math.round(totalScore / totalWeight) : 50;
  }

  /**
   * Calculate theme accessibility score
   */
  private calculateThemeAccessibilityScore(libraries: ComponentLibraryDetection[]): number {
    if (libraries.length === 0) return 50;

    let totalScore = 0;
    let count = 0;

    libraries.forEach((library) => {
      if (library.theme) {
        let themeScore = 50; // Base score

        if (library.theme.accessibility.hasHighContrast) themeScore += 20;
        if (library.theme.accessibility.hasReducedMotion) themeScore += 15;
        if (library.theme.accessibility.hasFocusVisible) themeScore += 15;

        totalScore += themeScore;
        count++;
      }
    });

    return count > 0 ? Math.round(totalScore / count) : 50;
  }

  /**
   * Generate recommendations
   */
  private generateRecommendations(
    libraries: ComponentLibraryDetection[],
    componentAnalysis: ComponentAccessibilityAnalysis[],
  ): string[] {
    const recommendations = new Set<string>();

    // Library-specific recommendations
    libraries.forEach((library) => {
      library.accessibility.recommendations.forEach((rec) => recommendations.add(rec));

      if (library.accessibility.score < 70) {
        recommendations.add(`Improve ${library.library} accessibility implementation`);
      }

      if (library.accessibility.missingA11yProps.length > 0) {
        recommendations.add(
          `Add missing accessibility properties: ${library.accessibility.missingA11yProps.join(', ')}`,
        );
      }
    });

    // Component-specific recommendations
    const lowScoreComponents = componentAnalysis.filter((comp) => comp.score < 60);
    if (lowScoreComponents.length > 0) {
      recommendations.add(
        `${lowScoreComponents.length} components need accessibility improvements`,
      );
    }

    const missingKeyboardSupport = componentAnalysis.filter((comp) => !comp.hasKeyboardSupport);
    if (missingKeyboardSupport.length > 0) {
      recommendations.add('Add keyboard support to interactive components');
    }

    const missingAccessibleNames = componentAnalysis.filter((comp) => !comp.hasAccessibleName);
    if (missingAccessibleNames.length > 0) {
      recommendations.add('Provide accessible names for all interactive elements');
    }

    return Array.from(recommendations).slice(0, 10);
  }

  /**
   * Generate best practices
   */
  private generateBestPractices(libraries: ComponentLibraryDetection[]): string[] {
    const bestPractices = new Set<string>();

    libraries.forEach((library) => {
      switch (library.library) {
        case 'material-ui':
          bestPractices.add('Use MUI accessibility props consistently across components');
          bestPractices.add('Leverage MUI theme customization for accessibility');
          bestPractices.add('Test with MUI accessibility testing tools');
          break;
        case 'ant-design':
          bestPractices.add('Follow Ant Design accessibility guidelines');
          bestPractices.add('Use Ant Design form validation with proper error announcements');
          bestPractices.add('Implement proper focus management in complex components');
          break;
        case 'chakra-ui':
          bestPractices.add('Utilize Chakra UI accessibility hooks and utilities');
          bestPractices.add('Customize Chakra theme with accessibility in mind');
          bestPractices.add('Use Chakra focus management features');
          break;
        case 'bootstrap':
          bestPractices.add('Implement ARIA patterns manually with Bootstrap');
          bestPractices.add('Use semantic HTML elements with Bootstrap classes');
          bestPractices.add('Add custom focus indicators for better visibility');
          break;
        case 'tailwind-ui':
          bestPractices.add('Combine Tailwind with Headless UI for accessibility');
          bestPractices.add('Implement ARIA patterns manually');
          bestPractices.add('Use Tailwind utilities for focus and hover states');
          break;
      }
    });

    // General best practices
    bestPractices.add('Test with screen readers and keyboard navigation');
    bestPractices.add('Ensure sufficient color contrast ratios');
    bestPractices.add('Provide clear focus indicators');
    bestPractices.add('Use semantic HTML elements');

    return Array.from(bestPractices).slice(0, 8);
  }

  /**
   * Identify common patterns
   */
  private identifyCommonPatterns(
    libraries: ComponentLibraryDetection[],
    componentAnalysis: ComponentAccessibilityAnalysis[],
  ): string[] {
    const patterns = new Set<string>();

    // Analyze component usage patterns
    const componentTypes = componentAnalysis.map((comp) => comp.componentType);
    const componentCounts = componentTypes.reduce(
      (acc, type) => {
        acc[type] = (acc[type] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    // Identify dominant patterns
    Object.entries(componentCounts).forEach(([type, count]) => {
      if (count > 3) {
        patterns.add(`Heavy use of ${type} components`);
      }
    });

    // Library-specific patterns
    libraries.forEach((library) => {
      if (library.confidence > 0.7) {
        patterns.add(`Primary ${library.library} implementation`);
      }

      if (library.theme?.hasCustomTheme) {
        patterns.add(`Custom ${library.library} theme implementation`);
      }
    });

    // Accessibility patterns
    const hasConsistentARIA = componentAnalysis.every((comp) => comp.hasARIAAttributes);
    if (hasConsistentARIA && componentAnalysis.length > 0) {
      patterns.add('Consistent ARIA attribute usage');
    }

    const hasGoodKeyboardSupport =
      componentAnalysis.length > 0 &&
      componentAnalysis.filter((comp) => comp.hasKeyboardSupport).length /
        componentAnalysis.length >
        0.8;
    if (hasGoodKeyboardSupport) {
      patterns.add('Good keyboard navigation support');
    }

    return Array.from(patterns).slice(0, 6);
  }

  /**
   * Helper methods
   */
  private getDefaultLibrary(): ComponentLibraryDetection {
    return {
      library: 'none',
      confidence: 0,
      components: [],
      accessibility: {
        score: 50,
        hasA11yProps: false,
        missingA11yProps: [],
        commonIssues: [],
        recommendations: [],
      },
    };
  }
}

export default ComponentLibraryDetector;
