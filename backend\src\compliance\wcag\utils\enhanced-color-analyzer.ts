/**
 * Enhanced Color Analyzer for WCAG Scanning
 * Advanced color contrast analysis with gradient, CSS custom properties, and complex background support
 * Integrated with proven third-party libraries for enhanced accuracy
 */

import { Page } from 'puppeteer';
import logger from '../../../utils/logger';

// Third-party contrast libraries for enhanced accuracy (with fallback)
let getContrastLib: {
  ratio: (fg: string, bg: string) => number;
  score: (fg: string, bg: string) => string;
  isAccessible: (fg: string, bg: string) => boolean;
} | null = null;
let ColorJSLib: {
  new (color: string): {
    contrast: (other: unknown, method: string) => number;
    deltaE: (other: unknown) => number;
    space?: { id: string };
  };
} | null = null;

try {
  getContrastLib = require('get-contrast');
} catch (error) {
  logger.debug('get-contrast library not available, using fallback calculations');
}

try {
  ColorJSLib = require('colorjs.io').default;
} catch (error) {
  logger.debug('colorjs.io library not available, using fallback calculations');
}

export interface ColorInfo {
  r: number;
  g: number;
  b: number;
  a: number;
  hex: string;
  hsl: { h: number; s: number; l: number };
  luminance: number;
}

export interface GradientInfo {
  type: 'linear' | 'radial' | 'conic';
  direction?: string;
  stops: Array<{
    color: ColorInfo;
    position: number; // 0-1
  }>;
  averageColor: ColorInfo;
  contrastRange: { min: number; max: number };
}

export interface BackgroundAnalysis {
  type: 'solid' | 'gradient' | 'image' | 'pattern' | 'complex';
  primaryColor?: ColorInfo;
  gradient?: GradientInfo;
  imageUrl?: string;
  hasTransparency: boolean;
  effectiveColor: ColorInfo; // Best representative color for contrast calculation
  confidence: number; // 0-1, how confident we are in the analysis
}

export interface TextAnalysis {
  text: string;
  color: ColorInfo;
  fontSize: number;
  fontWeight: string | number;
  isLarge: boolean; // >= 18pt regular or >= 14pt bold
  hasTextShadow: boolean;
  hasOutline: boolean;
  effectiveColor: ColorInfo; // Color after considering shadows/outlines
}

export interface ContrastResult {
  ratio: number;
  passes: {
    aa: boolean;
    aaa: boolean;
    aaLarge: boolean;
    aaaLarge: boolean;
  };
  recommendation: string;
  confidence: number;
  issues: string[];
}

export interface EnhancedColorAnalysisResult {
  element: string; // selector or xpath
  text: TextAnalysis;
  background: BackgroundAnalysis;
  contrast: ContrastResult;
  cssCustomProperties: Record<string, string>;
  computedStyles: Record<string, string>;
  accessibility: {
    isAccessible: boolean;
    level: 'AA' | 'AAA' | 'fail';
    improvements: string[];
  };
}

/**
 * Advanced color contrast analyzer with comprehensive background detection
 */
export class EnhancedColorAnalyzer {
  private static instance: EnhancedColorAnalyzer;
  private cssCustomPropsCache = new Map<string, Record<string, string>>();

  private constructor() {}

  static getInstance(): EnhancedColorAnalyzer {
    if (!EnhancedColorAnalyzer.instance) {
      EnhancedColorAnalyzer.instance = new EnhancedColorAnalyzer();
      // Log library availability on first instantiation
      const getContrastAvailable = !!getContrastLib;
      const colorJSAvailable = !!ColorJSLib;
      logger.info('EnhancedColorAnalyzer initialized', {
        getContrastLibrary: getContrastAvailable,
        colorJSLibrary: colorJSAvailable,
        enhancedAccuracy: getContrastAvailable || colorJSAvailable,
      });
    }
    return EnhancedColorAnalyzer.instance;
  }

  /**
   * Check if enhanced libraries are available
   */
  public hasEnhancedLibraries(): { getContrast: boolean; colorJS: boolean } {
    return {
      getContrast: !!getContrastLib,
      colorJS: !!ColorJSLib,
    };
  }

  /**
   * Analyze color contrast for all text elements on the page
   */
  async analyzePageContrast(page: Page): Promise<EnhancedColorAnalysisResult[]> {
    logger.debug('🎨 Starting enhanced color contrast analysis');

    // Inject enhanced color analysis functions
    await this.injectColorAnalysisFunctions(page);

    // Get all text elements with their computed styles
    const textElements = await page.evaluate(() => {
      return (
        window as unknown as {
          wcagEnhancedColorAnalysis: {
            analyzeAllTextElements: () => Array<{
              selector: string;
              text: string;
              element: string;
            }>;
          };
        }
      ).wcagEnhancedColorAnalysis.analyzeAllTextElements();
    });

    const results: EnhancedColorAnalysisResult[] = [];

    for (const element of textElements) {
      try {
        const analysis = await this.analyzeElementContrast(page, element);
        if (analysis) {
          results.push(analysis);
        }
      } catch (error) {
        logger.warn(`Failed to analyze element contrast: ${element.selector}`, {
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }

    logger.info(`✅ Enhanced color analysis completed: ${results.length} elements analyzed`);
    return results;
  }

  /**
   * Analyze contrast for a specific element
   */
  async analyzeElementContrast(
    page: Page,
    elementData: { selector: string; text: string; element: string },
  ): Promise<EnhancedColorAnalysisResult | null> {
    try {
      // Get enhanced element analysis
      const analysis = await page.evaluate((selector) => {
        return (
          window as unknown as {
            wcagEnhancedColorAnalysis: {
              analyzeElement: (selector: string) => {
                text: unknown;
                background: unknown;
                customProperties: Record<string, string>;
                computedStyles: Record<string, string>;
              } | null;
            };
          }
        ).wcagEnhancedColorAnalysis.analyzeElement(selector);
      }, elementData.selector);

      if (!analysis) return null;

      // Process the analysis data
      const textAnalysis = this.processTextAnalysis(analysis.text);
      const backgroundAnalysis = this.processBackgroundAnalysis(analysis.background);
      const contrastResult = this.calculateEnhancedContrast(textAnalysis, backgroundAnalysis);

      return {
        element: elementData.selector,
        text: textAnalysis,
        background: backgroundAnalysis,
        contrast: contrastResult,
        cssCustomProperties: analysis.customProperties || {},
        computedStyles: analysis.computedStyles || {},
        accessibility: this.assessAccessibility(contrastResult, textAnalysis),
      };
    } catch (error) {
      logger.warn(`Error analyzing element contrast: ${elementData.selector}`, {
        error: error instanceof Error ? error.message : String(error),
      });
      return null;
    }
  }

  /**
   * Inject enhanced color analysis functions into the page
   */
  private async injectColorAnalysisFunctions(page: Page): Promise<void> {
    await page.evaluateOnNewDocument(() => {
      interface WcagEnhancedColorAnalysis {
        analyzeAllTextElements: () => Array<{
          selector: string;
          text: string;
          element: string;
        }>;
        analyzeElement: (selector: string) => {
          text: unknown;
          background: unknown;
          customProperties: Record<string, string>;
          computedStyles: Record<string, string>;
        } | null;
        analyzeTextProperties: (element: HTMLElement, style: CSSStyleDeclaration) => unknown;
        analyzeBackgroundProperties: (element: HTMLElement, style: CSSStyleDeclaration) => unknown;
        extractCustomProperties: (element: HTMLElement) => Record<string, string>;
        getElementSelector: (element: HTMLElement) => string;
        getRelevantComputedStyles: (style: CSSStyleDeclaration) => Record<string, string>;
        parseColor: (colorString: string) => ColorInfo;
        isBoldWeight: (weight: string | number) => boolean;
        calculateEffectiveTextColor: (color: ColorInfo, style: CSSStyleDeclaration) => ColorInfo;
        analyzeGradientBackground: (background: string, _element: HTMLElement) => unknown;
        analyzeImageBackground: (
          background: string,
          style: CSSStyleDeclaration,
          _element: HTMLElement,
        ) => unknown;
        findEffectiveBackground: (element: HTMLElement) => unknown;
        resolveCustomProperty: (varString: string) => string;
        calculateLuminance: (r: number, g: number, b: number) => number;
        rgbToHex: (r: number, g: number, b: number) => string;
        rgbToHsl: (r: number, g: number, b: number) => { h: number; s: number; l: number };
        parseGradientStops?: (gradientString: string) => unknown;
        calculateAverageGradientColor?: (stops: unknown) => unknown;
        calculateGradientContrastRange?: (stops: unknown) => unknown;
      }

      (
        window as unknown as { wcagEnhancedColorAnalysis: WcagEnhancedColorAnalysis }
      ).wcagEnhancedColorAnalysis = {
        /**
         * Analyze all text elements on the page
         */
        analyzeAllTextElements() {
          const textElements: Array<{
            selector: string;
            text: string;
            element: string;
          }> = [];
          const walker = document.createTreeWalker(document.body, NodeFilter.SHOW_TEXT, {
            acceptNode: (node) => {
              const text = node.textContent?.trim();
              if (!text || text.length < 2) return NodeFilter.FILTER_REJECT;

              const parent = node.parentElement;
              if (!parent) return NodeFilter.FILTER_REJECT;

              // Skip hidden elements
              const style = window.getComputedStyle(parent);
              if (
                style.display === 'none' ||
                style.visibility === 'hidden' ||
                style.opacity === '0'
              ) {
                return NodeFilter.FILTER_REJECT;
              }

              return NodeFilter.FILTER_ACCEPT;
            },
          });

          let node;
          while ((node = walker.nextNode())) {
            const element = node.parentElement!;
            const selector = this.getElementSelector(element);

            textElements.push({
              selector,
              text: node.textContent?.trim() || '',
              element: element.tagName.toLowerCase(),
            });
          }

          return textElements;
        },

        /**
         * Analyze a specific element
         */
        analyzeElement(selector: string) {
          const element = document.querySelector(selector) as HTMLElement;
          if (!element) return null;

          const computedStyle = window.getComputedStyle(element);

          return {
            text: this.analyzeTextProperties(element, computedStyle),
            background: this.analyzeBackgroundProperties(element, computedStyle),
            customProperties: this.extractCustomProperties(element),
            computedStyles: this.getRelevantComputedStyles(computedStyle),
          };
        },

        /**
         * Analyze text properties
         */
        analyzeTextProperties(element: HTMLElement, style: CSSStyleDeclaration) {
          const color = this.parseColor(style.color);
          const fontSize = parseFloat(style.fontSize);
          const fontWeight = style.fontWeight;

          // Determine if text is large (18pt+ regular or 14pt+ bold)
          const isLarge = fontSize >= 18 || (fontSize >= 14 && this.isBoldWeight(fontWeight));

          // Check for text effects
          const hasTextShadow = style.textShadow !== 'none';
          const hasOutline =
            style.webkitTextStroke !== 'none' ||
            (style as unknown as { textStroke?: string }).textStroke !== 'none';

          // Calculate effective color (considering shadows/outlines)
          const effectiveColor = this.calculateEffectiveTextColor(color, style);

          return {
            text: element.textContent?.trim() || '',
            color,
            fontSize,
            fontWeight,
            isLarge,
            hasTextShadow,
            hasOutline,
            effectiveColor,
          };
        },

        /**
         * Analyze background properties with gradient and image support
         */
        analyzeBackgroundProperties(element: HTMLElement, style: CSSStyleDeclaration) {
          const background = style.background || style.backgroundColor;

          // Check for gradients
          if (background.includes('gradient')) {
            return this.analyzeGradientBackground(background, element);
          }

          // Check for images
          if (background.includes('url(')) {
            return this.analyzeImageBackground(background, style, element);
          }

          // Solid color background
          const backgroundColor = this.parseColor(style.backgroundColor);

          // Walk up the DOM tree to find effective background
          const effectiveBackground = this.findEffectiveBackground(element);

          return {
            type: 'solid',
            primaryColor: backgroundColor,
            hasTransparency: backgroundColor.a < 1,
            effectiveColor: effectiveBackground,
            confidence: backgroundColor.a === 0 ? 0.5 : 0.9,
          };
        },

        /**
         * Analyze gradient backgrounds
         */
        analyzeGradientBackground(background: string, _element: HTMLElement) {
          const gradientMatch = background.match(/(linear|radial|conic)-gradient\([^)]+\)/);
          if (!gradientMatch) {
            return { type: 'complex', confidence: 0.3 };
          }

          const gradientString = gradientMatch[0];
          const type = gradientMatch[1] as 'linear' | 'radial' | 'conic';

          // Parse gradient stops
          const stops = this.parseGradientStops(gradientString);

          // Calculate average color
          const averageColor = this.calculateAverageGradientColor(stops);

          // Calculate contrast range
          const contrastRange = this.calculateGradientContrastRange(stops);

          return {
            type: 'gradient',
            gradient: {
              type,
              stops,
              averageColor,
              contrastRange,
            },
            hasTransparency: stops.some(
              (stop: unknown) => (stop as { color: { a: number } }).color.a < 1,
            ),
            effectiveColor: averageColor,
            confidence: 0.7,
          };
        },

        /**
         * Analyze image backgrounds
         */
        analyzeImageBackground(
          background: string,
          style: CSSStyleDeclaration,
          _element: HTMLElement,
        ) {
          const urlMatch = background.match(/url\(['"]?([^'"]+)['"]?\)/);
          const imageUrl = urlMatch ? urlMatch[1] : '';

          // Try to get fallback background color
          const fallbackColor = this.parseColor(style.backgroundColor);

          // For images, we need to sample colors or use fallback
          return {
            type: 'image',
            imageUrl,
            primaryColor: fallbackColor,
            hasTransparency: true,
            effectiveColor: fallbackColor.a > 0 ? fallbackColor : { r: 128, g: 128, b: 128, a: 1 },
            confidence: fallbackColor.a > 0 ? 0.6 : 0.3,
          };
        },

        /**
         * Find effective background by walking up DOM tree
         */
        findEffectiveBackground(element: HTMLElement): unknown {
          let current = element;

          while (current && current !== document.body) {
            const style = window.getComputedStyle(current);
            const bgColor = this.parseColor(style.backgroundColor);

            if (bgColor.a > 0) {
              return bgColor;
            }

            current = current.parentElement!;
          }

          // Default to white background
          return { r: 255, g: 255, b: 255, a: 1, hex: '#ffffff' };
        },

        /**
         * Parse color string to color object
         */
        parseColor(colorString: string) {
          // Handle CSS custom properties
          if (colorString.startsWith('var(')) {
            colorString = this.resolveCustomProperty(colorString);
          }

          // Create a temporary element to parse color
          const temp = document.createElement('div');
          temp.style.color = colorString;
          document.body.appendChild(temp);

          const computed = window.getComputedStyle(temp).color;
          document.body.removeChild(temp);

          // Parse rgb/rgba
          const match = computed.match(/rgba?\(([^)]+)\)/);
          if (match) {
            const values = match[1].split(',').map((v) => parseFloat(v.trim()));
            const r = values[0] || 0;
            const g = values[1] || 0;
            const b = values[2] || 0;
            const a = values[3] !== undefined ? values[3] : 1;

            return {
              r,
              g,
              b,
              a,
              hex: this.rgbToHex(r, g, b),
              hsl: this.rgbToHsl(r, g, b),
              luminance: this.calculateLuminance(r, g, b),
            };
          }

          // Fallback to black
          return {
            r: 0,
            g: 0,
            b: 0,
            a: 1,
            hex: '#000000',
            hsl: { h: 0, s: 0, l: 0 },
            luminance: 0,
          };
        },

        /**
         * Resolve CSS custom property
         */
        resolveCustomProperty(varString: string): string {
          const match = varString.match(/var\(([^,)]+)(?:,\s*([^)]+))?\)/);
          if (!match) return varString;

          const propName = match[1].trim();
          const fallback = match[2]?.trim() || 'transparent';

          // Get the custom property value
          const value = getComputedStyle(document.documentElement).getPropertyValue(propName);
          return value.trim() || fallback;
        },

        /**
         * Extract CSS custom properties
         */
        extractCustomProperties(element: HTMLElement): Record<string, string> {
          const props: Record<string, string> = {};

          // Get all CSS custom properties from the element and its ancestors
          let current: HTMLElement | null = element;
          while (current) {
            const currentStyle = window.getComputedStyle(current);

            // Extract custom properties (this is a simplified approach)
            for (let i = 0; i < currentStyle.length; i++) {
              const prop = currentStyle[i];
              if (prop.startsWith('--')) {
                props[prop] = currentStyle.getPropertyValue(prop);
              }
            }

            current = current.parentElement;
          }

          return props;
        },

        /**
         * Calculate luminance for contrast ratio
         */
        calculateLuminance(r: number, g: number, b: number): number {
          const [rs, gs, bs] = [r, g, b].map((c) => {
            c = c / 255;
            return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
          });

          return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
        },

        /**
         * Helper functions
         */
        rgbToHex(r: number, g: number, b: number): string {
          return (
            '#' +
            [r, g, b]
              .map((x) => {
                const hex = Math.round(x).toString(16);
                return hex.length === 1 ? '0' + hex : hex;
              })
              .join('')
          );
        },

        rgbToHsl(r: number, g: number, b: number): { h: number; s: number; l: number } {
          r /= 255;
          g /= 255;
          b /= 255;
          const max = Math.max(r, g, b),
            min = Math.min(r, g, b);
          let h = 0,
            s = 0;
          const l = (max + min) / 2;

          if (max !== min) {
            const d = max - min;
            s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
            switch (max) {
              case r:
                h = (g - b) / d + (g < b ? 6 : 0);
                break;
              case g:
                h = (b - r) / d + 2;
                break;
              case b:
                h = (r - g) / d + 4;
                break;
            }
            h /= 6;
          }

          return { h: h * 360, s: s * 100, l: l * 100 };
        },

        isBoldWeight(weight: string | number): boolean {
          if (typeof weight === 'number') return weight >= 600;
          return ['bold', 'bolder', '600', '700', '800', '900'].includes(weight);
        },

        getElementSelector(element: HTMLElement): string {
          if (element.id) return `#${element.id}`;

          const path = [];
          while (element && element.nodeType === Node.ELEMENT_NODE) {
            let selector = element.nodeName.toLowerCase();
            if (element.className) {
              selector += '.' + element.className.split(' ').join('.');
            }
            path.unshift(selector);
            element = element.parentElement!;
            if (path.length > 5) break; // Limit depth
          }

          return path.join(' > ');
        },

        getRelevantComputedStyles(style: CSSStyleDeclaration): Record<string, string> {
          const relevant = [
            'color',
            'backgroundColor',
            'background',
            'fontSize',
            'fontWeight',
            'textShadow',
            'webkitTextStroke',
            'opacity',
            'filter',
          ];

          const styles: Record<string, string> = {};
          relevant.forEach((prop) => {
            styles[prop] = style.getPropertyValue(prop);
          });

          return styles;
        },

        /**
         * Calculate effective text color considering shadows and outlines
         */
        calculateEffectiveTextColor(color: unknown, _style: CSSStyleDeclaration): unknown {
          // For now, just return the original color
          // In a full implementation, this would analyze text shadows and outlines
          return color;
        },
      };
    });
  }

  /**
   * Process text analysis data
   */
  private processTextAnalysis(textData: unknown): TextAnalysis {
    const data = textData as Record<string, unknown>;
    return {
      text: (data.text as string) || '',
      color: (data.color as ColorInfo) || {
        r: 0,
        g: 0,
        b: 0,
        a: 1,
        hex: '#000000',
        hsl: { h: 0, s: 0, l: 0 },
        luminance: 0,
      },
      fontSize: (data.fontSize as number) || 16,
      fontWeight: (data.fontWeight as string | number) || 'normal',
      isLarge: (data.isLarge as boolean) || false,
      hasTextShadow: (data.hasTextShadow as boolean) || false,
      hasOutline: (data.hasOutline as boolean) || false,
      effectiveColor: (data.effectiveColor as ColorInfo) || (data.color as ColorInfo),
    };
  }

  /**
   * Process background analysis data
   */
  private processBackgroundAnalysis(backgroundData: unknown): BackgroundAnalysis {
    const data = backgroundData as Record<string, unknown>;
    return {
      type: (data.type as BackgroundAnalysis['type']) || 'solid',
      primaryColor: data.primaryColor as ColorInfo,
      gradient: data.gradient as GradientInfo,
      imageUrl: data.imageUrl as string,
      hasTransparency: (data.hasTransparency as boolean) || false,
      effectiveColor: (data.effectiveColor as ColorInfo) || {
        r: 255,
        g: 255,
        b: 255,
        a: 1,
        hex: '#ffffff',
        hsl: { h: 0, s: 0, l: 100 },
        luminance: 1,
      },
      confidence: (data.confidence as number) || 0.5,
    };
  }

  /**
   * Calculate enhanced contrast ratio with third-party library integration
   */
  private calculateEnhancedContrast(
    text: TextAnalysis,
    background: BackgroundAnalysis,
  ): ContrastResult {
    // Convert colors to hex format for third-party libraries
    const textHex = this.colorToHex(text.effectiveColor);
    const backgroundHex = this.colorToHex(background.effectiveColor);

    // Primary analysis with get-contrast (proven accuracy) or fallback
    let primaryRatio: number;
    let primaryScore: string | null = null;
    let isWCAGCompliant: boolean;

    if (getContrastLib) {
      try {
        primaryRatio = getContrastLib.ratio(textHex, backgroundHex);
        primaryScore = getContrastLib.score(textHex, backgroundHex);
        isWCAGCompliant = getContrastLib.isAccessible(textHex, backgroundHex);
        logger.debug('Using get-contrast library for enhanced accuracy');
      } catch (error) {
        logger.warn('get-contrast library failed, falling back to legacy calculation', { error });
        // Fallback to existing calculation
        const textLuminance =
          text.effectiveColor.luminance || this.calculateLuminance(text.effectiveColor);
        const bgLuminance =
          background.effectiveColor.luminance || this.calculateLuminance(background.effectiveColor);
        primaryRatio =
          (Math.max(textLuminance, bgLuminance) + 0.05) /
          (Math.min(textLuminance, bgLuminance) + 0.05);
        isWCAGCompliant = text.isLarge ? primaryRatio >= 3 : primaryRatio >= 4.5;
      }
    } else {
      // Fallback to existing calculation when library not available
      const textLuminance =
        text.effectiveColor.luminance || this.calculateLuminance(text.effectiveColor);
      const bgLuminance =
        background.effectiveColor.luminance || this.calculateLuminance(background.effectiveColor);
      primaryRatio =
        (Math.max(textLuminance, bgLuminance) + 0.05) /
        (Math.min(textLuminance, bgLuminance) + 0.05);
      isWCAGCompliant = text.isLarge ? primaryRatio >= 3 : primaryRatio >= 4.5;
    }

    // Advanced analysis with colorjs.io for complex color spaces
    let advancedAnalysis: {
      ratio: number;
      deltaE: number;
      colorSpace: string;
      isP3: boolean;
      isOKLCH: boolean;
    } | null = null;
    if (ColorJSLib && this.requiresAdvancedAnalysis(textHex, backgroundHex)) {
      try {
        const color1 = new ColorJSLib(textHex);
        const color2 = new ColorJSLib(backgroundHex);
        advancedAnalysis = {
          ratio: color1.contrast(color2, 'WCAG21'),
          deltaE: color1.deltaE(color2),
          colorSpace: color1.space?.id || 'unknown',
          isP3: color1.space?.id === 'p3' || color2.space?.id === 'p3',
          isOKLCH: color1.space?.id === 'oklch' || color2.space?.id === 'oklch',
        };
        logger.debug('Using colorjs.io for advanced color space analysis');
      } catch (error) {
        logger.debug('colorjs.io advanced analysis failed', { error });
      }
    }

    // Use the most accurate ratio available
    const finalRatio = advancedAnalysis?.ratio || primaryRatio;

    // WCAG contrast requirements (enhanced with library validation)
    const passes = {
      aa: text.isLarge ? finalRatio >= 3 : finalRatio >= 4.5,
      aaa: text.isLarge ? finalRatio >= 4.5 : finalRatio >= 7,
      aaLarge: finalRatio >= 3,
      aaaLarge: finalRatio >= 4.5,
    };

    const issues: string[] = [];
    let recommendation = '';

    if (!passes.aa) {
      issues.push('Does not meet WCAG AA contrast requirements');
      recommendation = `Increase contrast ratio to at least ${text.isLarge ? '3:1' : '4.5:1'} for AA compliance`;
    } else if (!passes.aaa) {
      recommendation = `Consider increasing contrast ratio to ${text.isLarge ? '4.5:1' : '7:1'} for AAA compliance`;
    } else {
      recommendation = 'Excellent contrast ratio - meets all WCAG requirements';
    }

    // Enhanced confidence calculation
    let confidence = background.confidence;

    // Adjust confidence based on background complexity
    if (background.type === 'gradient') {
      confidence *= 0.8;
      issues.push('Gradient background detected - manual verification recommended');
    } else if (background.type === 'image') {
      confidence *= 0.6;
      issues.push('Image background detected - manual verification required');
    }

    // Boost confidence if third-party libraries agree
    if (primaryScore && isWCAGCompliant === passes.aa) {
      confidence = Math.min(confidence * 1.1, 1.0);
    }

    // Add advanced color space information to issues if detected
    if (advancedAnalysis?.isP3) {
      issues.push('P3 color space detected - enhanced analysis applied');
    }
    if (advancedAnalysis?.isOKLCH) {
      issues.push('OKLCH color space detected - enhanced analysis applied');
    }

    return {
      ratio: Math.round(finalRatio * 100) / 100,
      passes,
      recommendation,
      confidence,
      issues,
    };
  }

  /**
   * Convert ColorInfo to hex format for third-party libraries
   */
  private colorToHex(color: ColorInfo): string {
    const toHex = (c: number) => {
      const hex = Math.round(c).toString(16);
      return hex.length === 1 ? '0' + hex : hex;
    };
    return `#${toHex(color.r)}${toHex(color.g)}${toHex(color.b)}`;
  }

  /**
   * Determine if advanced color space analysis is required
   */
  private requiresAdvancedAnalysis(color1: string, color2: string): boolean {
    // Check for CSS color functions that might indicate advanced color spaces
    const advancedColorPatterns = [
      /color\(/i,
      /oklch\(/i,
      /oklab\(/i,
      /lch\(/i,
      /lab\(/i,
      /display-p3/i,
      /rec2020/i,
    ];

    return advancedColorPatterns.some((pattern) => pattern.test(color1) || pattern.test(color2));
  }

  /**
   * Calculate luminance for a color (legacy fallback)
   */
  private calculateLuminance(color: ColorInfo): number {
    const { r, g, b } = color;
    const [rs, gs, bs] = [r, g, b].map((c) => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });

    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  }

  /**
   * Assess accessibility level
   */
  private assessAccessibility(
    contrast: ContrastResult,
    text: TextAnalysis,
  ): {
    isAccessible: boolean;
    level: 'AA' | 'AAA' | 'fail';
    improvements: string[];
  } {
    const improvements: string[] = [];

    if (!contrast.passes.aa) {
      improvements.push('Increase color contrast to meet WCAG AA standards');
      improvements.push('Consider using darker text or lighter background colors');

      return {
        isAccessible: false,
        level: 'fail',
        improvements,
      };
    }

    if (!contrast.passes.aaa) {
      improvements.push('Consider increasing contrast for AAA compliance');
      improvements.push('Test with users who have visual impairments');
    }

    if (text.hasTextShadow || text.hasOutline) {
      improvements.push('Verify that text effects do not interfere with readability');
    }

    return {
      isAccessible: true,
      level: contrast.passes.aaa ? 'AAA' : 'AA',
      improvements,
    };
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cssCustomPropsCache.clear();
  }
}

export default EnhancedColorAnalyzer;
